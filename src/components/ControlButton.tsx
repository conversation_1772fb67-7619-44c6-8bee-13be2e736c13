import React from "react";

interface ControlButtonProps {
  onClick: () => void;
  type: "music" | "sound";
  isActive: boolean;
  size?: "small" | "medium" | "big";
  className?: string;
}

/**
 * Control Button Component
 * 
 * A reusable button component for controlling music and sound settings.
 * Features:
 * - Different types: music and sound
 * - Active/inactive states with visual feedback
 * - Multiple sizes
 * - Hover effects and animations
 */
export const ControlButton: React.FC<ControlButtonProps> = ({
  onClick,
  type,
  isActive,
  size = "medium",
  className = "",
}) => {
  /**
   * Get button configuration based on type and state
   */
  const getButtonConfig = () => {
    const baseConfig = {
      music: {
        activeIcon: "🎵",
        inactiveIcon: "🔇",
        activeText: "Música Activada",
        inactiveText: "Música Desactivada",
        activeColor: "#28a745",
        inactiveColor: "#6c757d",
        activeBgColor: "#d4edda",
        inactiveBgColor: "#f8f9fa",
        activeBorderColor: "#c3e6cb",
        inactiveBorderColor: "#dee2e6",
      },
      sound: {
        activeIcon: "🔊",
        inactiveIcon: "🔇",
        activeText: "Voz Activada",
        inactiveText: "Voz Desactivada",
        activeColor: "#17a2b8",
        inactiveColor: "#6c757d",
        activeBgColor: "#d1ecf1",
        inactiveBgColor: "#f8f9fa",
        activeBorderColor: "#bee5eb",
        inactiveBorderColor: "#dee2e6",
      },
    };

    const config = baseConfig[type];
    
    return {
      icon: isActive ? config.activeIcon : config.inactiveIcon,
      text: isActive ? config.activeText : config.inactiveText,
      color: isActive ? config.activeColor : config.inactiveColor,
      bgColor: isActive ? config.activeBgColor : config.inactiveBgColor,
      borderColor: isActive ? config.activeBorderColor : config.inactiveBorderColor,
    };
  };

  /**
   * Get size-specific styles
   */
  const getSizeStyles = () => {
    const sizeStyles = {
      small: {
        padding: "8px 12px",
        fontSize: "12px",
        iconSize: "14px",
        gap: "6px",
        borderRadius: "6px",
      },
      medium: {
        padding: "12px 16px",
        fontSize: "14px",
        iconSize: "16px",
        gap: "8px",
        borderRadius: "8px",
      },
      big: {
        padding: "16px 20px",
        fontSize: "16px",
        iconSize: "20px",
        gap: "10px",
        borderRadius: "10px",
      },
    };

    return sizeStyles[size];
  };

  const config = getButtonConfig();
  const sizeStyles = getSizeStyles();

  return (
    <button
      onClick={onClick}
      className={`control-button ${className}`}
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        gap: sizeStyles.gap,
        padding: sizeStyles.padding,
        backgroundColor: config.bgColor,
        color: config.color,
        border: `2px solid ${config.borderColor}`,
        borderRadius: sizeStyles.borderRadius,
        cursor: "pointer",
        fontSize: sizeStyles.fontSize,
        fontWeight: "600",
        boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
        transition: "all 0.3s ease",
        minWidth: "fit-content",
        whiteSpace: "nowrap",
      }}
      onMouseOver={(e) => {
        e.currentTarget.style.transform = "translateY(-2px)";
        e.currentTarget.style.boxShadow = "0 4px 12px rgba(0,0,0,0.15)";
        e.currentTarget.style.opacity = "0.9";
      }}
      onMouseOut={(e) => {
        e.currentTarget.style.transform = "translateY(0)";
        e.currentTarget.style.boxShadow = "0 2px 8px rgba(0,0,0,0.1)";
        e.currentTarget.style.opacity = "1";
      }}
      title={config.text}
    >
      <span style={{ fontSize: sizeStyles.iconSize }}>{config.icon}</span>
      {size === "big" && <span>{config.text}</span>}
    </button>
  );
};
