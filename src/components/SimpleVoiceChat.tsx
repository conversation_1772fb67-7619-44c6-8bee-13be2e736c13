import React, { useEffect, useRef, useState } from "react";
import { useRealTimeConversation } from "../hooks/useRealTimeConversation";
import { GameProgress } from "./GameProgress";
import { HintsPopup } from "./HintsPopup";
import { AnimatedVoiceButton } from "./AnimatedVoiceButton";
import { ConversationStateIndicator } from "./ConversationStateIndicator";
import { ChatArea } from "./ChatArea";
import { CharacterInfo } from "./CharacterInfo";
import { MovistarInfo } from "./MovistarInfo";
import { ConversationStorage } from "../services/ConversationStorage";
import { ControlButton } from "./ControlButton";
import { audioStateManager } from "../services/AudioStateManager";
import { VoicesService } from "../services/VoicesService";
import { stopBackgroundMusic, startBackgroundMusic } from "../utils/audioUtils";
import type { GameProgress as GameProgressType } from "../services/ConversationStorage";

interface SimpleVoiceChatProps {
  generatedCharacter?: string;
  isGameStarted: boolean;
  initialMessage?: string;
  onGameEnd?: (gameWon: boolean) => void;
}

/**
 * Main voice chat component for the character guessing game
 *
 * Features:
 * - Real-time voice conversation with AI
 * - Game progress tracking
 * - Hints system integration
 * - Automatic conversation state management
 */

export const SimpleVoiceChat: React.FC<SimpleVoiceChatProps> = ({
  generatedCharacter,
  isGameStarted,
  initialMessage,
  onGameEnd,
}) => {
  // Refs and state for game management
  const autoStartAttempted = useRef(false);
  const gameEndTriggered = useRef(false);
  const [gameProgress, setGameProgress] = useState<GameProgressType | null>(null);
  const [showHintsPopup, setShowHintsPopup] = useState(false);
  const [showCharacterInfo, setShowCharacterInfo] = useState(false);
  const [showMovistarInfo, setShowMovistarInfo] = useState(false);
  const [showGameEndScreen, setShowGameEndScreen] = useState(false);
  const conversationStorage = useRef(ConversationStorage.getInstance());

  // Estados para controlar música y speech
  const [isMusicPlaying, setIsMusicPlaying] = useState(true);
  const [currentAzureTTSState, setCurrentAzureTTSState] = useState(true);
  const [audioState, setAudioState] = useState(audioStateManager.getState());

  // Instancias de servicios (singleton)
  const voicesService = VoicesService.getInstance();

  // Hook for real-time conversation management
  const {
    isActive,
    conversationState,
    messages,
    isSupported,
    error,
    startConversation,
    stopConversation,
    enableSmartMicrophone,
    addInitialMessage,
  } = useRealTimeConversation(generatedCharacter, isGameStarted, gameProgress?.gameFinished);

  /**
   * Add initial message when it arrives
   */
  useEffect(() => {
    if (initialMessage && isGameStarted) {
      addInitialMessage(initialMessage);
    }
  }, [initialMessage, isGameStarted, addInitialMessage]);

  /**
   * Update game progress when session changes
   */
  useEffect(() => {
    if (isGameStarted) {
      const updateProgress = () => {
        const progress = conversationStorage.current.getGameProgress();
        setGameProgress(progress);

        // Notify parent when game finishes (only once)
        if (progress?.gameFinished && !gameEndTriggered.current && onGameEnd) {
          console.log('🎮 Game finished detected:', { gameFinished: progress.gameFinished, gameWon: progress.gameWon });
          gameEndTriggered.current = true;
          setShowGameEndScreen(true);
          onGameEnd(progress.gameWon);
        }
      };

      updateProgress();
      const interval = setInterval(updateProgress, 1000);
      return () => clearInterval(interval);
    }
  }, [isGameStarted, messages, onGameEnd]);

  /**
   * Efecto para monitorear cambios en el estado del audio y sincronizar TTS
   */
  useEffect(() => {
    const updateAudioState = () => {
      const newState = audioStateManager.getState();
      setAudioState(newState);
      setIsMusicPlaying(newState.isBackgroundMusicPlaying);
    };

    // Sincronizar estado inicial del TTS con el servicio de voces
    const syncTTSState = () => {
      const isVoiceConfigured = voicesService.isVoiceConfigured();
      setCurrentAzureTTSState(isVoiceConfigured);
    };

    // Actualizar estados iniciales
    updateAudioState();
    syncTTSState();

    // Polling para monitorear cambios (ya que subscribe está comentado)
    const interval = setInterval(() => {
      updateAudioState();
      syncTTSState();
    }, 1000);

    return () => clearInterval(interval);
  }, [voicesService]);

  useEffect(() => {
    // console.log("🔄 conversationState cambió a:", conversationState);

    // Auto-start evaluation when state becomes idle
    if (
      conversationState === "idle" &&
      isGameStarted &&
      !isActive &&
      isSupported &&
      !error &&
      initialMessage &&
      !autoStartAttempted.current
    ) {
      // console.log("🎤 Estado cambió a idle, verificando auto-start...");
    }
  }, [conversationState, isGameStarted, isActive, isSupported, error, initialMessage]);

  /**
   * Reset flags when game restarts
   */
  useEffect(() => {
    if (!isGameStarted) {
      autoStartAttempted.current = false;
      gameEndTriggered.current = false;
    }
  }, [isGameStarted]);

  /**
   * Stop conversation when game finishes
   */
  useEffect(() => {
    if (gameProgress?.gameFinished && isActive) {
      console.log('🔇 Game finished, stopping conversation');
      stopConversation();
    }
  }, [gameProgress?.gameFinished, isActive, stopConversation]);

  /**
   * Auto-open Movistar info when character is generated and game starts
   */
  useEffect(() => {
    if (isGameStarted && generatedCharacter && generatedCharacter.trim()) {
      // Delay opening to allow the game to fully initialize
      const timer = setTimeout(() => {
        setShowMovistarInfo(true);
      }, 2000); // 2 seconds delay

      return () => clearTimeout(timer);
    }
  }, [isGameStarted, generatedCharacter]);

  /**
   * Handle voice button click - start/stop conversation
   */
  const handleButtonClick = () => {
    // Don't allow microphone activation if game has finished
    if (gameProgress?.gameFinished) {
      console.log('🔇 Game has finished, microphone disabled');
      return;
    }

    if (isActive) {
      stopConversation();
    } else {
      startConversation().then((success) => {
        if (success) {
          enableSmartMicrophone();
        }
      });
    }
  };

  /**
   * Handle hints popup display
   */
  const handleShowHints = () => setShowHintsPopup(true);
  const handleCloseHints = () => setShowHintsPopup(false);

  /**
   * Handle character info display
   */
  const handleShowCharacterInfo = () => setShowCharacterInfo(true);
  const handleCloseCharacterInfo = () => setShowCharacterInfo(false);

  /**
   * Handle Movistar info display
   */
  const handleShowMovistarInfo = () => setShowMovistarInfo(true);
  const handleCloseMovistarInfo = () => setShowMovistarInfo(false);

  /**
   * Handle game end screen display
   */
  const handleCloseGameEndScreen = () => setShowGameEndScreen(false);

  const handleNewGame = () => {
    setShowGameEndScreen(false);
    // Reset game by triggering a page reload or calling parent reset function
    window.location.reload();
  };

  /**
   * Manejar click en el botón de música
   */
  const handleMusicClick = async () => {
    if (audioState.isBackgroundMusicPlaying) {
      // Detener música
      stopBackgroundMusic();
      setIsMusicPlaying(false);
      console.log("🔇 Música de fondo detenida");
    } else {
      // Iniciar música
      try {
        await startBackgroundMusic('/assets/sounds/sound.mp3');
        setIsMusicPlaying(true);
        console.log("🎵 Música de fondo iniciada");
      } catch (error) {
        console.warn("⚠️ Error iniciando música de fondo:", error);
        setIsMusicPlaying(false);
      }
    }
  };

  /**
   * Manejar click en el botón de speech/TTS
   */
  const handleSpeechClick = async () => {
    if (currentAzureTTSState) {
      // Desactivar TTS
      voicesService.disableVoice();
      setCurrentAzureTTSState(false);
      console.log("🔇 Servicio de TTS desactivado");
    } else {
      // Activar TTS
      try {
        const isConfigured = await voicesService.enableVoice();
        if (isConfigured) {
          setCurrentAzureTTSState(true);
          console.log("🔊 Servicio de TTS activado");
        } else {
          console.warn("⚠️ No se pudo configurar el servicio de TTS");
          setCurrentAzureTTSState(false);
        }
      } catch (error) {
        console.error("❌ Error configurando TTS:", error);
        setCurrentAzureTTSState(false);
      }
    }
  };

  if (!isGameStarted) {
    return null;
  }

  if (!isSupported) {
    return (
      <div
        style={{
          backgroundColor: "#fff3cd",
          border: "2px solid #ffc107",
          borderRadius: "12px",
          padding: "20px",
          marginTop: "20px",
          textAlign: "center",
        }}
      >
        <h4 style={{ color: "#856404", marginBottom: "8px" }}>
          ⚠️ Reconocimiento de voz no disponible
        </h4>
        <p style={{ color: "#856404", margin: 0 }}>
          Tu navegador no soporta esta funcionalidad
        </p>
      </div>
    );
  }

  return (
    <>
      <div className="voice-chat-container">
        <div className="character-title" style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', gap: '12px' }}>
          <span>{generatedCharacter}</span>
          {generatedCharacter && (
            <div style={{ display: 'flex', gap: '8px' }}>
              <button
                onClick={handleShowCharacterInfo}
                style={{
                  backgroundColor: '#007bff',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  padding: '6px 12px',
                  fontSize: '12px',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px',
                  fontWeight: '500'
                }}
                onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#0056b3'}
                onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#007bff'}
                title="Ver información del personaje"
              >
                📖 Info
              </button>
              <button
                onClick={handleShowMovistarInfo}
                style={{
                  backgroundColor: '#e91e63',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  padding: '6px 12px',
                  fontSize: '12px',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px',
                  fontWeight: '500'
                }}
                onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#c2185b'}
                onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#e91e63'}
                title="Ver contenido en Movistar+"
              >
                🎬 Movistar+
              </button>
            </div>
          )}
        </div>

        {/* Progreso del juego */}
        {gameProgress && (
          <GameProgress
            gameProgress={gameProgress}
            onShowHints={handleShowHints}
          />
        )}

        {/* Conversation State Indicators */}
        <ConversationStateIndicator conversationState={conversationState} />

        {/* Animated Voice Button */}
        <div className="voice-button-container">
          <AnimatedVoiceButton
            state={gameProgress?.gameFinished ? "disabled" : conversationState}
            isActive={isActive && !gameProgress?.gameFinished}
            onClick={handleButtonClick}
            disabled={gameProgress?.gameFinished}
          />
        </div>

        {/* Control Buttons Section */}
        <div style={{ marginTop: "20px", textAlign: "center" }}>
          <small
            style={{ color: "#666", display: "block", marginBottom: "10px" }}
          >
            🎛️ Controles de Audio:
          </small>
          <div
            style={{
              display: "flex",
              gap: "12px",
              justifyContent: "center",
              flexWrap: "wrap",
            }}
          >
            <ControlButton
              onClick={handleMusicClick}
              type="music"
              isActive={isMusicPlaying && audioState.isBackgroundMusicPlaying}
              size="big"
            />

            <ControlButton
              type="sound"
              isActive={currentAzureTTSState}
              size="big"
              onClick={handleSpeechClick}
              className="speech-control"
            />
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="error-message">
            ❌ {error}
          </div>
        )}

        {/* Chat Area */}
        <ChatArea
          messages={messages}
          isActive={isActive}
          isGameStarted={isGameStarted}
          initialMessage={initialMessage}
        />

        {/* Status Indicator */}
        <div className="status-indicator">
          {gameProgress?.gameFinished
            ? "🎯 Juego finalizado • El micrófono está deshabilitado"
            : "💡 Habla cuando veas el micrófono pulsando • El sistema gestiona todo automáticamente"
          }
        </div>

        {/* Debug Information */}
        <div className="debug-info">
          Debug: isActive={isActive.toString()}, state={conversationState}, attempted={autoStartAttempted.current.toString()}
        </div>
      </div>

      {/* Hints Popup */}
      <HintsPopup
        hints={gameProgress?.hints || []}
        isOpen={showHintsPopup}
        onClose={handleCloseHints}
      />

      {/* Character Info Modal */}
      <CharacterInfo
        characterName={generatedCharacter || ''}
        isVisible={showCharacterInfo}
        onClose={handleCloseCharacterInfo}
      />

      {/* Movistar Info Modal */}
      <MovistarInfo
        characterName={generatedCharacter || ''}
        isVisible={showMovistarInfo}
        onClose={handleCloseMovistarInfo}
      />

      {/* Game End Screen - Now handled by parent App component */}
      {/* {showGameEndScreen && gameProgress && (
        <GameEndScreen
          gameWon={gameProgress.gameWon}
          onClose={handleCloseGameEndScreen}
          onNewGame={handleNewGame}
        />
      )} */}
    </>
  );
};
