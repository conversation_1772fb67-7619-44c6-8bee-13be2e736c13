import React from "react";

interface GameStartScreenProps {
  onStartGame: () => void;
  onShowRules: () => void;
  onGameEnd: (won: boolean) => void;
  aiLoading: boolean;
}

/**
 * Game Start Screen Component
 *
 * Displays the main menu with game controls and options.
 * Features:
 * - Game start button
 * - Rules button
 * - Test buttons for development
 * - Cookie management utilities
 */
export const GameStartScreen: React.FC<GameStartScreenProps> = ({
  onStartGame,
  onShowRules,
  onGameEnd,
  aiLoading,
}) => {
  return (
    <div className="card">
      <div className="game-container">
        <h3 className="game-title">🎯 Juego de Adivinanza de Personajes</h3>

        {/* Welcome Back Section */}
        <div className="quick-start-section">
          <h4 className="quick-start-title">👋 ¡Bienvenido de vuelta!</h4>
          <p className="quick-start-description">
            ¿Estás listo para poner a prueba tu ingenio? Se generará un
            personaje misterioso y tendrás que adivinarlo haciendo preguntas
            inteligentes. ¡Usa tu voz para interactuar con la IA y descubre
            quién se esconde detrás del misterio!
          </p>

          <div
            className="buttons-container"
            style={{
              display: "flex",
              gap: "12px",
              flexWrap: "wrap",
              justifyContent: "center",
            }}
          >
            <button
              onClick={onStartGame}
              disabled={aiLoading}
              className="primary-button"
              style={{ flex: "1", minWidth: "200px" }}
            >
              {aiLoading ? "Iniciando Juego..." : "🎮 INICIAR JUEGO"}
            </button>

            <button
              onClick={onShowRules}
              className="secondary-button"
              style={{
                backgroundColor: "#6c757d",
                color: "white",
                border: "none",
                borderRadius: "8px",
                padding: "12px 24px",
                fontSize: "16px",
                fontWeight: "600",
                cursor: "pointer",
                transition: "all 0.2s ease",
                flex: "0 0 auto",
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = "#5a6268";
                e.currentTarget.style.transform = "translateY(-1px)";
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = "#6c757d";
                e.currentTarget.style.transform = "translateY(0)";
              }}
            >
              📋 Ver Reglas
            </button>
          </div>

          {/* Temporary test buttons - Remove in production */}
          <div style={{ marginTop: "20px", textAlign: "center" }}>
            <small
              style={{ color: "#666", display: "block", marginBottom: "10px" }}
            >
              🧪 Botones de prueba (remover en producción):
            </small>
            <div
              style={{
                display: "flex",
                gap: "8px",
                justifyContent: "center",
                flexWrap: "wrap",
              }}
            >
              <button
                onClick={() => onGameEnd(true)}
                style={{
                  backgroundColor: "#28a745",
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  padding: "6px 12px",
                  fontSize: "12px",
                  cursor: "pointer",
                }}
              >
                🎉 Simular Victoria
              </button>
              <button
                onClick={() => onGameEnd(false)}
                style={{
                  backgroundColor: "#dc3545",
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  padding: "6px 12px",
                  fontSize: "12px",
                  cursor: "pointer",
                }}
              >
                😔 Simular Derrota
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
